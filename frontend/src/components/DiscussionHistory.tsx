import React, { useState } from 'react';
import { useApp } from '../context/AppContext';
import { storageService } from '../services/storageService';
import { Discussion } from '../types';
import {
  History,
  MessageSquare,
  Users,
  Calendar,
  TrendingUp,
  Search,
  ChevronDown,
  ChevronUp,
  Trash2,
  CheckCircle,
  Clock
} from 'lucide-react';

export const DiscussionHistory: React.FC = () => {
  const { state, dispatch } = useApp();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'topic' | 'messages'>('date');
  const [filterStatus, setFilterStatus] = useState<'all' | 'consensus' | 'ended'>('all');
  const [expandedDiscussion, setExpandedDiscussion] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const handleDeleteDiscussion = async (discussionId: string) => {
    if (!confirm('确定要删除这条讨论记录吗？此操作不可撤销。')) {
      return;
    }

    try {
      setDeletingId(discussionId);
      
      // 从服务器删除
      await storageService.deleteDiscussion(discussionId);
      
      // 重新加载所有讨论数据
      const updatedDiscussions = await storageService.getDiscussions();
      dispatch({ 
        type: 'SET_ALL_DISCUSSIONS', 
        payload: updatedDiscussions 
      });
      
    } catch (error) {
      console.error('删除讨论失败:', error);
      alert('删除失败，请重试');
    } finally {
      setDeletingId(null);
    }
  };

  // 过滤和排序讨论
  const filteredDiscussions = state.allDiscussions
    .filter(discussion => {
      const matchesSearch = discussion.topic.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || discussion.status === filterStatus;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'topic':
          return a.topic.localeCompare(b.topic);
        case 'messages':
          return b.messages.length - a.messages.length;
        default:
          return 0;
      }
    });

  const getStatusIcon = (status: Discussion['status']) => {
    switch (status) {
      case 'consensus':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'ended':
        return <Clock className="w-4 h-4 text-gray-500" />;
      default:
        return <MessageSquare className="w-4 h-4 text-blue-500" />;
    }
  };

  const getStatusText = (status: Discussion['status']) => {
    switch (status) {
      case 'consensus':
        return '已达成共识';
      case 'ended':
        return '已结束';
      default:
        return '进行中';
    }
  };

  return (
    <div className="h-screen bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-hidden">
      <div className="h-full p-6">
        <div className="bg-white rounded-xl shadow-lg h-full flex flex-col">
          {/* 头部 */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-t-xl">
            <div className="flex items-center gap-3 mb-4">
              <History size={32} />
              <div>
                <h1 className="text-2xl font-bold">讨论历史</h1>
                <p className="text-purple-100">查看和管理历史讨论记录</p>
              </div>
            </div>

            {/* 搜索和过滤 */}
            <div className="flex gap-4 items-center">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="搜索讨论话题..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 rounded-lg text-gray-900 placeholder-gray-500"
                />
              </div>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-4 py-2 rounded-lg text-gray-900"
              >
                <option value="date">按时间排序</option>
                <option value="topic">按话题排序</option>
                <option value="messages">按消息数排序</option>
              </select>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-4 py-2 rounded-lg text-gray-900"
              >
                <option value="all">全部状态</option>
                <option value="consensus">已达成共识</option>
                <option value="ended">已结束</option>
              </select>
            </div>
          </div>

          {/* 讨论列表 */}
          <div className="flex-1 overflow-y-auto p-6">
            {filteredDiscussions.length === 0 ? (
              <div className="text-center py-12">
                <History size={64} className="text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无讨论记录</h3>
                <p className="text-gray-600">开始一个新的讨论来创建历史记录</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredDiscussions.map((discussion) => {
                  const participants = state.agents.filter(agent => 
                    discussion.participants.includes(agent.id)
                  );

                  return (
                    <div key={discussion.id} className="bg-gray-50 rounded-lg border border-gray-200">
                      <div className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              {getStatusIcon(discussion.status)}
                              <h3 className="font-semibold text-gray-900">{discussion.topic}</h3>
                              <span className="text-sm text-gray-500">
                                {getStatusText(discussion.status)}
                              </span>
                            </div>

                            <div className="flex items-center gap-6 text-sm text-gray-600">
                              <div className="flex items-center gap-1">
                                <Calendar className="w-4 h-4" />
                                {new Date(discussion.createdAt).toLocaleString()}
                              </div>
                              <div className="flex items-center gap-1">
                                <MessageSquare className="w-4 h-4" />
                                {discussion.messages.length} 条消息
                              </div>
                              <div className="flex items-center gap-1">
                                <Users className="w-4 h-4" />
                                {participants.length} 位参与者
                              </div>
                              {discussion.consensusScore !== undefined && (
                                <div className="flex items-center gap-1">
                                  <TrendingUp className="w-4 h-4" />
                                  共识度 {Math.round(discussion.consensusScore)}%
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handleDeleteDiscussion(discussion.id)}
                              disabled={deletingId === discussion.id}
                              className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                              title="删除记录"
                            >
                              {deletingId === discussion.id ? (
                                <div className="animate-spin w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full"></div>
                              ) : (
                                <Trash2 className="w-4 h-4" />
                              )}
                            </button>
                            <button
                              onClick={() => setExpandedDiscussion(
                                expandedDiscussion === discussion.id ? null : discussion.id
                              )}
                              className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors"
                              title="查看详情"
                            >
                              {expandedDiscussion === discussion.id ? (
                                <ChevronUp className="w-4 h-4" />
                              ) : (
                                <ChevronDown className="w-4 h-4" />
                              )}
                            </button>
                          </div>
                        </div>

                        {/* 展开的详细信息 */}
                        {expandedDiscussion === discussion.id && (
                          <div className="mt-4 pt-4 border-t border-gray-200">
                            {/* 参与者信息 */}
                            <div className="mb-4">
                              <h4 className="font-medium text-gray-900 mb-2">参与者</h4>
                              <div className="flex flex-wrap gap-2">
                                {participants.map((agent) => (
                                  <div key={agent.id} className="flex items-center gap-2 bg-white px-3 py-1 rounded-full border">
                                    <img
                                      src={agent.avatar}
                                      alt={agent.name}
                                      className="w-6 h-6 rounded-full object-cover"
                                    />
                                    <span className="text-sm font-medium">{agent.name}</span>
                                  </div>
                                ))}
                              </div>
                            </div>

                            {/* 共识结论 */}
                            {discussion.consensus && (
                              <div className="mb-4">
                                <h4 className="font-medium text-gray-900 mb-2">讨论结论</h4>
                                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                                  <p className="text-green-800">{discussion.consensus}</p>
                                </div>
                              </div>
                            )}

                            {/* 消息预览 */}
                            <div>
                              <h4 className="font-medium text-gray-900 mb-2">消息预览</h4>
                              <div className="space-y-2 max-h-60 overflow-y-auto">
                                {discussion.messages.slice(0, 5).map((message) => {
                                  const agent = participants.find(a => a.id === message.agentId);
                                  return (
                                    <div key={message.id} className="bg-white p-3 rounded border">
                                      <div className="flex items-center gap-2 mb-1">
                                        <span className="font-medium text-sm">{agent?.name}</span>
                                        <span className="text-xs text-gray-500">
                                          {new Date(message.timestamp).toLocaleTimeString()}
                                        </span>
                                      </div>
                                      <p className="text-sm text-gray-700 line-clamp-2">{message.content}</p>
                                    </div>
                                  );
                                })}
                                {discussion.messages.length > 5 && (
                                  <div className="text-center text-sm text-gray-500">
                                    还有 {discussion.messages.length - 5} 条消息...
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DiscussionHistory;
